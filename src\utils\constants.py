"""
常量定义模块
定义项目中使用的所有常量
"""

# 文件路径常量
import os

# 获取项目根目录（从当前文件位置推断）
def _get_project_root():
    """获取项目根目录"""
    current_file = os.path.abspath(__file__)
    # src/utils/constants.py -> 项目根目录
    return os.path.dirname(os.path.dirname(os.path.dirname(current_file)))

PROJECT_ROOT = _get_project_root()

ASSETS_DIR = os.path.join(PROJECT_ROOT, "assets")
IMAGES_DIR = os.path.join(ASSETS_DIR, "images")
APK_DIR = os.path.join(ASSETS_DIR, "apk")
OUTPUT_DIR = os.path.join(PROJECT_ROOT, "output")
RESULTS_DIR = os.path.join(OUTPUT_DIR, "results")
LOGS_DIR = os.path.join(OUTPUT_DIR, "logs")
CONFIG_DIR = os.path.join(PROJECT_ROOT, "config")

# APK文件名
ATX_APK = "ATX.apk"

# 设备相关常量
VIVO_GALLERY_PACKAGE = "com.vivo.gallery"
UIAUTOMATOR_PACKAGE = "uiautomator"

# 灰阶图片相关常量
GREY_IMAGE_PREFIX = "P3_gray"
GREY_IMAGE_SUFFIX = "jpg"
MAX_GREY_INDEX = 64  # 白色
MIN_GREY_INDEX = 0   # 黑色

# 亮度相关常量
MIN_BRIGHTNESS = 1
MAX_BRIGHTNESS = 255

# 帧率相关常量
SMART_FPS_MODE = 1
STANDARD_FPS_MODE = 60
HIGH_FPS_MODE = 120

# ADB命令相关常量
ADB_COMMANDS = {
    'devices': 'adb devices',
    'root': 'adb root',
    'vivo_root': 'adb vivoroot',
    'wait_device': 'adb wait-for-device',
    'wake_screen': 'adb shell input keyevent KEYCODE_WAKEUP',
    'power_key': 'adb shell input keyevent KEYCODE_POWER',
    'unlock_screen': 'adb shell input keyevent KEYCODE_MENU',
    'home_key': 'adb shell input keyevent KEYCODE_HOME',
    'get_screen_lock_status': 'adb shell dumpsys power | grep "mWakefulness"',
    'auto_brightness_on': 'adb shell settings put system screen_brightness_mode 1',
    'auto_brightness_off': 'adb shell settings put system screen_brightness_mode 0',
    'get_auto_brightness': 'adb shell settings get system screen_brightness_mode',
    'dark_mode_on': 'adb shell cmd uimode night yes',
    'dark_mode_off': 'adb shell cmd uimode night no',
    'get_dark_mode': 'adb shell cmd uimode night',
    'set_fps_smart': 'adb shell settings put global vivo_screen_refresh_rate_mode 1',
    'set_fps_60': 'adb shell settings put global vivo_screen_refresh_rate_mode 60',
    'set_fps_120': 'adb shell settings put global vivo_screen_refresh_rate_mode 120',
    'stay_awake_on': 'adb shell settings put global stay_on_while_plugged_in 7',
    'stay_awake_off': 'adb shell settings put global stay_on_while_plugged_in 0',
    'get_stay_awake': 'adb shell settings get global stay_on_while_plugged_in',
    'airplane_mode_on': 'adb shell settings put global airplane_mode_on 1',
    'airplane_mode_off': 'adb shell settings put global airplane_mode_on 0',
    'get_airplane_mode': 'adb shell settings get global airplane_mode_on',
    'wifi_enable': 'adb shell svc wifi enable',
    'wifi_disable': 'adb shell svc wifi disable',
}

# 日志相关常量
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'

# 测试相关常量
TEST_TYPES = {
    'LTPO': 'ltpo',
    'LTPS': 'ltps'
}

# 错误消息
ERROR_MESSAGES = {
    'NO_DEVICE': '无法连接设备，请确认设备状态...',
    'NO_ROOT': '无法获取root权限',
    'NO_GREY_STRATEGY': '该设备不存在灰阶策略',
    'NO_NIT_VALUE': '无法获取到亮度值，请确认设备状态...',
    'ATX_INSTALL_FAILED': 'ATX安装失败',
}

# 成功消息
SUCCESS_MESSAGES = {
    'ATX_INSTALLED': 'ATX安装成功',
    'ROOT_OBTAINED': 'root权限获取成功',
    'TEST_COMPLETED': '测试完成',
    'FILE_SAVED': '文件保存成功',
}

# 默认配置值
DEFAULT_CONFIG = {
    'test_timeout': 300,  # 测试超时时间（秒）
    'retry_count': 3,     # 重试次数
    'wait_time': 2,       # 默认等待时间（秒）
    'fps_check_count': 5, # FPS检查次数
    'remote_path': '/sdcard/',  # 设备存储路径（Android路径，始终使用/）
    'test_settings': {
        'ltpo': {
            'brightness_step': 1,
            'fps_sample_count': 8,
            'fps_sample_interval': 0.25,
            'fps_confidence_threshold': 0.6,
            'fps_max_retries': 3,
            'enable_gradient_test': True,
            'enable_threshold_test': True,
            'enable_detailed_stats': True
        },
        'ltps': {
            'test_black_image': True,
            'test_white_image': True,
            'grey_test_step': 8,
            'threshold_search_method': 'binary'
        }
    }
}

# LTPO帧率检测相关常量
LTPO_FPS_CONFIG = {
    'DEFAULT_SAMPLE_COUNT': 8,      # 默认采样次数
    'DEFAULT_SAMPLE_INTERVAL': 0.25, # 默认采样间隔（秒）
    'MIN_SAMPLE_COUNT': 3,          # 最小采样次数
    'MAX_SAMPLE_COUNT': 15,         # 最大采样次数
    'MIN_SAMPLE_INTERVAL': 0.1,     # 最小采样间隔（秒）
    'MAX_SAMPLE_INTERVAL': 1.0,     # 最大采样间隔（秒）
    'CONFIDENCE_THRESHOLD': 0.6,    # 置信度阈值
    'OUTLIER_THRESHOLD': 2.0,       # 异常值检测阈值（标准差倍数）
}
