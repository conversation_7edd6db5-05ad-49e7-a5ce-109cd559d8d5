"""
基础测试类
提供测试的通用功能和抽象接口
"""

import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from ..core.device_manager import DeviceManager
from ..core.ui_automation import Grey<PERSON>caleImageController
from ..data.log_collector import DataCollectionManager
from ..data.data_processor import DataProcessor
from ..data.file_manager import FileManager
from ..utils.constants import MIN_BRIGHTNESS, MAX_BRIGHTNESS, ERROR_MESSAGES
from ..utils.config import config_manager
from ..utils.logger import get_logger


class BaseTest(ABC):
    """基础测试类"""
    
    def __init__(self, desktop_test: bool = False, enable_inspection: bool = True):
        """
        初始化基础测试
        
        Args:
            desktop_test: 是否为桌面测试
            enable_inspection: 是否启用灰阶校验
        """
        self.desktop_test = desktop_test
        self.enable_inspection = enable_inspection
        self.logger = get_logger()
        
        # 初始化组件
        self.device_manager = DeviceManager()
        self.image_controller = GreyScaleImageController(desktop_test)
        self.data_collector = DataCollectionManager()
        self.data_processor = DataProcessor()
        self.file_manager = FileManager()

        # UI自动化状态
        self.ui_automation_available = False
        
        # 测试配置
        self.config = config_manager
        self.test_timeout = self.config.get('test_timeout', 300)
        self.retry_count = self.config.get('retry_count', 3)
        self.wait_time = self.config.get('wait_time', 2)
        
        # 测试数据
        self.brightness_data = []
        self.grey_data = []
        self.test_results = []
    
    def initialize(self) -> bool:
        """
        初始化测试环境
        包括设备初始化、UI自动化、数据预热、屏幕解锁和常亮设置

        Returns:
            初始化是否成功
        """
        try:
            if self.logger:
                self.logger.info("开始初始化测试环境...")
            
            # 初始化设备
            if not self.device_manager.initialize_device():
                raise RuntimeError("设备初始化失败")

            # 初始化图片控制器
            try:
                if self.image_controller.initialize():
                    self.ui_automation_available = self.image_controller.ui_available
                    if self.logger:
                        ui_status = "可用" if self.ui_automation_available else "不可用"
                        self.logger.info(f"UI自动化状态: {ui_status}")
                else:
                    if self.logger:
                        self.logger.warning("图片控制器初始化失败")
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"图片控制器初始化异常: {e}")
                self.ui_automation_available = False

            # 启动数据收集
            self._prewarm_data()

            # 确保屏幕解锁并设置常亮模式
            try:
                if self.logger:
                    self.logger.info("开始设置屏幕管理...")

                # 确保屏幕处于解锁状态
                if not self.device_manager.ensure_screen_unlocked():
                    if self.logger:
                        self.logger.warning("屏幕解锁失败，但测试将继续进行")

                # 设置屏幕常亮模式（充电时不锁屏）
                if not self.device_manager.set_screen_stay_awake(True):
                    if self.logger:
                        self.logger.warning("设置屏幕常亮模式失败，测试期间可能出现屏幕休眠")
                else:
                    if self.logger:
                        self.logger.info("屏幕常亮模式已开启，测试期间屏幕将保持常亮")

            except Exception as screen_error:
                if self.logger:
                    self.logger.warning(f"屏幕管理设置失败: {screen_error}，测试将继续进行")

            if self.logger:
                self.logger.info("测试环境初始化完成")

            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"测试环境初始化失败: {e}")
            return False

    def _prewarm_data(self, timeout: float = 10.0) -> None:
        """预热数据采集：启动监控并等待nit/fps准备好（尽力而为）。"""
        try:
            self.data_collector.start_monitoring(prewarm_timeout=timeout,
                                                require_nit=True,
                                                require_fps=True)
            ready = self.data_collector.wait_until_ready(timeout=timeout)
            if self.logger:
                if ready:
                    self.logger.info("数据预热完成，nit/fps已就绪")
                else:
                    self.logger.warning("数据预热未完全就绪，将在测试中继续采集与回退")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"预热数据失败: {e}")
    
    def cleanup(self) -> None:
        """清理测试环境"""
        try:
            # 停止数据收集
            self.data_collector.stop_monitoring()

            # 清理图片控制器
            try:
                self.image_controller.cleanup()
            except Exception as e:
                if self.logger:
                    self.logger.debug(f"图片控制器清理失败: {e}")

            # 清理设备
            self.device_manager.cleanup()

            if self.logger:
                self.logger.info("测试环境清理完成")

        except Exception as e:
            if self.logger:
                self.logger.error(f"测试环境清理失败: {e}")
    
    def check_grey_scale_strategy(self) -> bool:
        """
        检查设备是否支持灰阶策略

        Returns:
            是否支持灰阶策略
        """
        try:
            if self.logger:
                self.logger.info("检查灰阶策略...")

            # 如果UI自动化不可用，给出友好提示
            if not self.ui_automation_available:
                if self.logger:
                    self.logger.info("UI自动化服务不可用，将跳过灰阶策略检查")
                    self.logger.info("这不会影响测试的核心功能，程序将继续运行")
                return True  # 假设支持，让测试继续进行

            # 尝试打开黑色图片
            if not self.image_controller.open_grey_image(0):
                if self.logger:
                    self.logger.warning("无法打开黑色灰阶图片，跳过策略检查")
                return True  # 假设支持，让测试继续进行

            self.device_manager.set_brightness(1)
            # time.sleep(self.wait_time)
            time.sleep(8)

            # 获取黑色图片的FPS
            black_data = self.data_collector.get_current_data()
            black_fps = black_data.get('fps', 0)

            # 尝试打开白色图片
            if not self.image_controller.open_grey_image(64):
                if self.logger:
                    self.logger.warning("无法打开白色灰阶图片，跳过策略检查")
                return True  # 假设支持，让测试继续进行

            # time.sleep(self.wait_time)
            time.sleep(8)

            # 获取白色图片的FPS
            white_data = self.data_collector.get_current_data()
            white_fps = white_data.get('fps', 0)

            if self.logger:
                self.logger.info(f"黑色图片FPS: {black_fps}, 白色图片FPS: {white_fps}")

            # 如果两个FPS相同且都是60，说明没有灰阶策略
            has_strategy = not (black_fps == white_fps == 60)

            if has_strategy:
                if self.logger:
                    self.logger.info("设备支持灰阶策略")
            else:
                if self.logger:
                    self.logger.warning("设备不支持灰阶策略")

            return has_strategy

        except Exception as e:
            if self.logger:
                self.logger.warning(f"检查灰阶策略时出现问题: {e}")
                self.logger.info("将跳过灰阶策略检查，继续执行测试")
            return True  # 出现异常时假设支持，让测试继续进行
    
    def wait_for_stable_fps(self, timeout: int = 10) -> Optional[int]:
        """
        等待FPS稳定
        
        Args:
            timeout: 超时时间
            
        Returns:
            稳定的FPS值
        """
        start_time = time.time()
        fps_samples = []
        
        while time.time() - start_time < timeout:
            try:
                data = self.data_collector.get_current_data()
                fps = data.get('fps')
                
                if fps is not None:
                    fps_samples.append(fps)
                    
                    # 如果有足够的样本，检查是否稳定
                    if len(fps_samples) >= 3:
                        recent_samples = fps_samples[-3:]
                        if all(fps == recent_samples[0] for fps in recent_samples):
                            return recent_samples[0]
                
                time.sleep(0.5)
                
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"获取FPS失败: {e}")
                time.sleep(0.5)
        
        # 如果没有稳定值，返回最后一个有效值
        if fps_samples:
            return fps_samples[-1]
        
        return None
    
    def binary_search_brightness_threshold(self, 
                                         low: int, 
                                         high: int, 
                                         expected_fps: int, 
                                         target_fps: int,
                                         grey_index: int = 0) -> int:
        """
        二分查找亮度门限
        
        Args:
            low: 最低亮度
            high: 最高亮度
            expected_fps: 期望的低亮度FPS
            target_fps: 目标高亮度FPS
            grey_index: 灰阶图片索引
            
        Returns:
            门限亮度值
        """
        try:
            while low < high:
                mid = (low + high) // 2
                
                # 设置亮度并等待稳定
                self.device_manager.set_brightness(mid)
                time.sleep(self.wait_time)
                
                # 获取当前FPS
                current_fps = self.wait_for_stable_fps()
                
                if self.logger:
                    self.logger.debug(f"亮度 {mid}: FPS {current_fps}")
                
                if current_fps == expected_fps:
                    low = mid + 1
                else:
                    high = mid
            
            return low
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"二分查找亮度门限失败: {e}")
            return low
    
    def collect_brightness_gradient_data(self, 
                                       start_brightness: int = MIN_BRIGHTNESS,
                                       end_brightness: int = MAX_BRIGHTNESS,
                                       step: int = 1,
                                       grey_index: int = 0) -> List[Dict[str, Any]]:
        """
        收集亮度梯度数据
        
        Args:
            start_brightness: 起始亮度
            end_brightness: 结束亮度
            step: 亮度步长
            grey_index: 灰阶图片索引
            
        Returns:
            亮度数据列表
        """
        data = []
        
        try:
            # 打开指定灰阶图片
            self.image_controller.open_grey_image(grey_index)
            
            for brightness in range(start_brightness, end_brightness + 1, step):
                # 设置亮度
                self.device_manager.set_brightness(brightness)
                
                # 每10个亮度值重置一次屏幕策略
                if brightness % 10 == 0:
                    self.device_manager.reset_screen_policy()
                
                time.sleep(self.wait_time)
                
                # 收集数据
                current_data = self.data_collector.collect_brightness_data(brightness)
                data.append(current_data)
                
                if self.logger:
                    self.logger.info(f"亮度 {brightness}: {current_data}")
                
                # 如果FPS降到1或更低，停止测试
                fps = current_data.get('fps', 0)
                if fps <= 1:
                    if self.logger:
                        self.logger.info(f"FPS降至 {fps}，停止亮度梯度测试")
                    break
            
            return data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"收集亮度梯度数据失败: {e}")
            return data
    
    @abstractmethod
    def run_test(self) -> bool:
        """
        运行测试（抽象方法，由子类实现）
        
        Returns:
            测试是否成功
        """
        pass
    
    @abstractmethod
    def get_test_type(self) -> str:
        """
        获取测试类型（抽象方法，由子类实现）
        
        Returns:
            测试类型字符串
        """
        pass
    
    def save_results(self, additional_info: Optional[Dict] = None) -> str:
        """
        保存测试结果
        
        Args:
            additional_info: 额外信息
            
        Returns:
            保存的文件路径
        """
        try:
            # 处理测试数据
            if self.brightness_data and self.grey_data:
                self.test_results = self.data_processor.merge_brightness_grey_data(
                    self.brightness_data, self.grey_data
                )
            elif self.test_results:
                # 如果已有处理好的结果，直接使用
                pass
            else:
                if self.logger:
                    self.logger.warning("没有测试数据可保存")
                return ""
            
            # 保存结果
            filepath = self.file_manager.save_test_results(
                test_data=self.test_results,
                test_type=self.get_test_type(),
                brightness_data=self.brightness_data,
                additional_info=additional_info
            )
            
            return filepath
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"保存测试结果失败: {e}")
            return ""
    
    def execute_test(self) -> bool:
        """
        执行完整的测试流程
        
        Returns:
            测试是否成功
        """
        try:
            # 初始化
            if not self.initialize():
                return False
            
            # 检查灰阶策略（如果启用校验）
            # if self.enable_inspection:
            #     if not self.check_grey_scale_strategy():
            #         raise RuntimeError(ERROR_MESSAGES['NO_GREY_STRATEGY'])
            #
            #     if self.logger:
            #         self.logger.info("设备支持灰阶策略，开始测试...")

            # 运行具体测试
            success = self.run_test()
            
            if success:
                # 保存结果
                result_file = self.save_results()
                if result_file:
                    if self.logger:
                        self.logger.info(f"测试完成，结果已保存到: {result_file}")
                else:
                    if self.logger:
                        self.logger.warning("测试完成，但保存结果失败")
            
            return success
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"测试执行失败: {e}")
            return False
        finally:
            # 清理环境
            self.cleanup()
